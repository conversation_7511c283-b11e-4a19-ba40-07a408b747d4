-- START NYM-2930 --
-- =============================================
-- Bulk Claim Feature - Tenant Database Migration
-- =============================================

-- Create bulk_claim_job table in nymbl_training template database
CREATE TABLE IF NOT EXISTS nymbl_training.bulk_claim_job (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    job_id VARCHAR(36) NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    status VARCHAR(20) NOT NULL,
    total_claims INT NOT NULL DEFAULT 0,
    processed_claims INT NOT NULL DEFAULT 0,
    successful_claims INT NOT NULL DEFAULT 0,
    failed_claims INT NOT NULL DEFAULT 0,
    current_payer VARCHAR(100),
    error_message TEXT,
    completed BOOLEAN NOT NULL DEFAULT FALSE,
    x12_file_path VARCHAR(255) DEFAULT NULL,
    x12_file_content LONGTEXT DEFAULT NULL,
    created_by VARCHAR(50),
    created_date TIMESTAMP,
    last_modified_by VARCHAR(50),
    last_modified_date TIMESTAMP,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    version INT DEFAULT 0
);

-- Create audit table in nymbl_training template database
CREATE TABLE IF NOT EXISTS nymbl_training.bulk_claim_job_audit (
    id BIGINT NOT NULL,
    revision_id BIGINT NOT NULL,
    revision_type TINYINT,
    job_id VARCHAR(36),
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    status VARCHAR(20),
    total_claims INT,
    processed_claims INT,
    successful_claims INT,
    failed_claims INT,
    current_payer VARCHAR(100),
    error_message TEXT,
    completed BOOLEAN,
    x12_file_path VARCHAR(255),
    x12_file_content LONGTEXT,
    created_by VARCHAR(50),
    created_date TIMESTAMP,
    last_modified_by VARCHAR(50),
    last_modified_date TIMESTAMP,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    version INT,
    PRIMARY KEY (id, revision_id)
);

DELIMITER //

CREATE PROCEDURE add_column_if_not_exists(
    IN p_table VARCHAR(100),
    IN p_column VARCHAR(100),
    IN p_definition VARCHAR(250)
)
BEGIN
    SET @table = p_table;
    SET @column = p_column;
    SET @definition = p_definition;

    SET @check_sql = CONCAT(
        'SELECT COUNT(*) INTO @exists FROM information_schema.columns ',
        'WHERE table_schema = DATABASE() ',
        'AND table_name = "', @table, '" ',
        'AND column_name = "', @column, '"'
    );

    PREPARE check_stmt FROM @check_sql;
    EXECUTE check_stmt;
    DEALLOCATE PREPARE check_stmt;

    IF @exists = 0 THEN
        SET @add_column = CONCAT(
            'ALTER TABLE ', @table,
            ' ADD COLUMN ', @column, ' ', @definition
        );
        PREPARE add_stmt FROM @add_column;
        EXECUTE add_stmt;
        DEALLOCATE PREPARE add_stmt;
    END IF;
END //

CREATE PROCEDURE add_model_stub_columns(IN p_table VARCHAR(100))
BEGIN
    CALL add_column_if_not_exists(p_table, 'created_at', 'TIMESTAMP NULL');
    CALL add_column_if_not_exists(p_table, 'updated_at', 'TIMESTAMP NULL');
    CALL add_column_if_not_exists(p_table, 'version', 'INT DEFAULT 0');
END //

DELIMITER ;

-- Process main tables in nymbl_training template database
CALL add_model_stub_columns('nymbl_training.bulk_claim_job');
CALL add_model_stub_columns('nymbl_training.claim_file');
CALL add_model_stub_columns('nymbl_training.claim_submission');
CALL add_model_stub_columns('nymbl_training.claim');

-- Add bulk claim columns to claim table in nymbl_training
CALL add_column_if_not_exists('nymbl_training.claim', 'bulk_claim_job_id', 'VARCHAR(36) DEFAULT NULL');
CALL add_column_if_not_exists('nymbl_training.claim', 'bulk_submission', 'TINYINT(1) DEFAULT 0');

-- Process audit tables in nymbl_training template database
CALL add_model_stub_columns('nymbl_training.bulk_claim_job_audit');
CALL add_model_stub_columns('nymbl_training.claim_file_audit');
CALL add_model_stub_columns('nymbl_training.claim_submission_audit');
CALL add_model_stub_columns('nymbl_training.claim_audit');

-- Add bulk claim columns to claim_audit table in nymbl_training
CALL add_column_if_not_exists('nymbl_training.claim_audit', 'bulk_claim_job_id', 'VARCHAR(36) DEFAULT NULL');
CALL add_column_if_not_exists('nymbl_training.claim_audit', 'bulk_submission', 'TINYINT(1) DEFAULT 0');

-- Add x12 file columns to bulk_claim_job_audit table in nymbl_training
CALL add_column_if_not_exists('nymbl_training.bulk_claim_job_audit', 'x12_file_path', 'VARCHAR(255) DEFAULT NULL');
CALL add_column_if_not_exists('nymbl_training.bulk_claim_job_audit', 'x12_file_content', 'LONGTEXT DEFAULT NULL');

-- Add the "enable_bulk_claims" system setting to nymbl_training template database
INSERT INTO nymbl_training.system_setting (field, value, section)
SELECT 'enable_bulk_claims', 'N', 'billing'
FROM dual
WHERE NOT EXISTS (
    SELECT 1 FROM nymbl_training.system_setting WHERE field = 'enable_bulk_claims' AND section = 'billing'
);

-- Cleanup procedures
DROP PROCEDURE IF EXISTS add_model_stub_columns;
DROP PROCEDURE IF EXISTS add_column_if_not_exists;
-- END NYM-2930 --
