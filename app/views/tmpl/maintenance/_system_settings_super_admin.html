<legend class="form-section-header">Manual Cron - Superadmin Only</legend>
<div class="row ml-0">
    <button ng-click="manualCronService.dailyGeneralLedger()"
            class="btn btn-rounded btn-blue" id="daily-general-ledger"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        Run Daily GL
    </button>
    <button ng-click="manualCronService.pecosVerification()"
            class="btn btn-rounded btn-blue" id="pecos-verification"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        Run PECOS Verification
    </button>
    <button ng-click="manualCronService.dailyClearingHouse()"
            class="btn btn-rounded btn-blue" id="daily-clearing-house"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        Run Daily Rental Eligibility
    </button>
    <button ng-click="manualCronService.nymblDataDictionary()"
            class="btn btn-rounded btn-blue" id="nymbl-data-dictionary"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        Data Dictionary
    </button>
    <button ng-click="manualCronService.submitClaims()"
            class="btn btn-rounded btn-blue" id="submit-claims"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        Submit Claims
    </button>
</div>

<legend class="form-section-header">Quicksight S3 Exports</legend>
<div class="row ml-0">
    <button ng-click="manualCronService.qsClinicalOperations()"
            class="btn btn-rounded btn-blue" id="qs-clinical-operations"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        Clinical Operations
    </button>
    <button ng-click="manualCronService.qsGeneralLedger()"
            class="btn btn-rounded btn-blue" id="qs-general-ledger"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        General Ledger
    </button>
    <button ng-click="manualCronService.qsPrescriptionSummary()"
            class="btn btn-rounded btn-blue" id="qs-prescription-summary"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        Prescription Summary
    </button>
    <button ng-click="manualCronService.qsPurchasingHistory()"
            class="btn btn-rounded btn-blue" id="qs-purchasing-history"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        Purchasing History
    </button>
    <button ng-click="manualCronService.qsNymblStatusHistory()"
            class="btn btn-rounded btn-blue" id="qs-nymbl-status-history"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        Nymbl Status History
    </button>
    <button ng-click="manualCronService.qsAiNotesUsage()"
            class="btn btn-rounded btn-blue" id="qs-ai-notes-usage"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        AI Notes Usage
    </button>
</div>
<legend class="form-section-header">Quicksight S3 Exports (System Wide)</legend>
<div class="row ml-0">
    <button ng-click="manualCronService.userPermissions()"
            class="btn btn-rounded btn-blue" id="user-permissions"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        User Permissions Info
    </button>
    <button ng-click="manualCronService.assetUserPermissions()"
            class="btn btn-rounded btn-blue" id="asset-user-permissions"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        User Asset Permissions Info
    </button>
</div>
<legend class="form-section-header">Create AI Notes S3 Bucket (Only available when AI Notes is active - Authorized Personnel Only!!!)</legend>
<div class="row ml-0">
    <button ng-click="manualCronService.createS3Bucket()"
            class="btn btn-rounded btn-blue" id="create-s3-ai"
            ng-show="aiNotesActive"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        Create AI Note Bucket
    </button>
</div>
<legend class="form-section-header">Create Lmn Custom Mapping (Only available when LMN is active - Authorized Personnel Only!!!)</legend>
<div class="row ml-0">
    <button ng-click="manualCronService.createLmnTemplate()"
            class="btn btn-rounded btn-blue" id="create-lmn-template"
            ng-show="lmnActive"
            data-loading-text="<i class='fa fa-spinner fa-spin fa-pulse'></i> Processing...">
        Configure LMN Templates
    </button>
</div>

