app.factory('DownloadFactory', DownloadFactory);
DownloadFactory.$inject = ['$resource'];

function DownloadFactory($resource) {
	return $resource('api/download', {}, {
		download: {
			url: 'api/download/x12/837/claim/:claimId',
			method: 'GET',
			params: {
				claimId: '@_claimId',
				download: 'true'
			},
			transformResponse: function (data, headers) {
				// For X12 file downloads, we need access to custom headers and the file content
				// Return an object that includes both the data and headers for easy access
				return {
					data: data,
					header: headers()
				};
			}
		},
		downloadBulkX12: {
			url: 'api/download/x12/837/bulk/:jobId',
			method: 'GET',
			params: {
				jobId: '@_jobId',
				download: 'true'
			},
			transformResponse: function (data, headers) {
				// For bulk X12 file downloads, we need access to custom headers and the file content
				// Return an object that includes both the data and headers for easy access
				return {
					data: data,
					header: headers()
				};
			}
		}
	});
}
