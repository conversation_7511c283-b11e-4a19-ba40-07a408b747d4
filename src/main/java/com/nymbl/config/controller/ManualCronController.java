package com.nymbl.config.controller;

import com.nymbl.config.clearingHouse.ClearingHouseSSH;
import com.nymbl.config.clearingHouse.ClearingHouseSSHv2;
import com.nymbl.config.service.QuicksightExportService;
import com.nymbl.tenant.service.GeneralLedgerService;
import com.nymbl.tenant.service.PhysicianService;
import com.nymbl.tenant.service.PrescriptionService;
import com.nymbl.tenant.service.SystemSettingService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/api/manual-cron")
public class ManualCronController {

    private final GeneralLedgerService generalLedgerService;
    private final PhysicianService physicianService;
    private final PrescriptionService prescriptionService;
    private final ClearingHouseSSH clearingHouseSSH;
    private final ClearingHouseSSHv2 clearingHouseSSHv2;
    private final QuicksightExportService quicksightExportService;
    private final SystemSettingService systemSettingService;

    @GetMapping(value = "/daily/generalLedger")
    public void runDailyGeneralLedger(HttpServletRequest request) {
        generalLedgerService.runDailyGeneralLedgerHistoryPopulation();
        generalLedgerService.executeStaticGeneralLedgerProcess();
    }

    @GetMapping(value = "/pecos")
    public void verifyPecosForTenant(HttpServletRequest request) {
        physicianService.pecosVerificationUpdate();
    }

    @GetMapping(value = "/daily/clearingHouse")
    public void dailyClearingHouse(HttpServletRequest request) {
        prescriptionService.checkEligibility(DateTime.now().plusDays(1).toDate());
    }

    @GetMapping(value = "/daily/user-permissions")
    public void userInfoByRegion(HttpServletRequest request) {
        quicksightExportService.uploadUserPermissionsCsvS3Quicksight();
    }

    /**
     * This is a duplicate in UploadController.java
     *
     * @param request
     */
    @GetMapping(value = "/daily/submit-claims")
    public void submitClaims(HttpServletRequest request) {
        String dch = systemSettingService.findBySectionAndField("billing", "default_clearing_house").getValue();
        if ("1".equals(dch)) { // 1 = Waystar
            clearingHouseSSH.submitClaimsToClearingHouse();
        } else {
            clearingHouseSSHv2.submitClaimsToClearingHouse();
        }
    }

    @GetMapping(value = "/quicksight/clinical-operations")
    public void quicksightClinicalOperations(HttpServletRequest request) {
        quicksightExportService.uploadClinicalOperationsCsvS3Quicksight();
    }

    @GetMapping(value = "/quicksight/general-ledger")
    public void quicksightGeneralLedger(HttpServletRequest request) {
        quicksightExportService.uploadGeneralLedgerCsvS3Quicksight();
    }

    @GetMapping(value = "/quicksight/prescription-summary")
    public void quicksightPrescriptionSummary(HttpServletRequest request) {
        quicksightExportService.uploadPrescriptionSummaryCsvS3Quicksight();
    }

    @GetMapping(value = "/quicksight/purchasing-history")
    public void quicksightPurchasingHistory(HttpServletRequest request) {
        quicksightExportService.uploadPurchasingHistoryCsvS3Quicksight();
    }

    @GetMapping(value = "/quicksight/nymbl-status-history")
    public void quicksightNymblStatusHistory(HttpServletRequest request) {
        quicksightExportService.uploadNymblStatusHistoryCsvS3Quicksight();
    }

    @GetMapping(value = "/quicksight/ai-notes-usage")
    public void quicksightAiNotesUsage(HttpServletRequest request) {
        quicksightExportService.uploadAiNotesUsageCsvS3Quicksight();
    }

    @GetMapping(value = "/quicksight/asset-user-permissions")
    public void quicksightUserPermissions(HttpServletRequest request) {
        quicksightExportService.uploadAssetUserPermissionsCsvS3Quicksight();
    }
}
