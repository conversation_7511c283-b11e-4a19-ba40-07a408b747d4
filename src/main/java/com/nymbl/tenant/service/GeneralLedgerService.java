package com.nymbl.tenant.service;

import com.nymbl.config.enums.GeneralLedgerFields;
import com.nymbl.config.model.FullName;
import com.nymbl.config.service.AbstractTableService;
import com.nymbl.config.service.QuicksightExportService;
import com.nymbl.config.utils.StringUtil;
import com.nymbl.tenant.TenantContext;
import com.nymbl.tenant.Timed;
import com.nymbl.tenant.batch.JobRunner;
import com.nymbl.tenant.model.GL_Period;
import com.nymbl.tenant.model.GeneralLedger;
import com.nymbl.tenant.model.GeneralLedgerUnion;
import com.nymbl.tenant.repository.GL_PeriodRepository;
import com.nymbl.tenant.repository.GeneralLedgerRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.batch.core.JobExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.List;

import static com.nymbl.config.SQLConstantsReports.gLImportSQL;
import static com.nymbl.tenant.repository.GeneralLedgerRepository.truncateLedgerTableForHistoryPull;

/**
 * Created by Brett Rowell 06/16/2021
 * All Business logic for General Ledger
 */
@Slf4j
@Service
public class GeneralLedgerService extends AbstractTableService<GeneralLedger, Long> {

    @Value("${upload.directory}")
    private String uploadDirectory;

    @Value("${db.url}")
    private String url;

    @Value("${spring.profiles.active}")
    private String activeProfiles;

    private final GeneralLedgerRepository generalLedgerRepository;
    private final JobRunner jobRunner;
    private final GL_PeriodRepository glPeriodRepository;
    private final FeatureFlagService featureFlagService;
    private final QuicksightExportService quicksightExportService;

    @Autowired
    public GeneralLedgerService(GeneralLedgerRepository generalLedgerRepository,
                                JobRunner jobRunner,
                                GL_PeriodRepository glPeriodRepository,
                                FeatureFlagService featureFlagService,
                                QuicksightExportService quicksightExportService) {
        super(generalLedgerRepository);
        this.generalLedgerRepository = generalLedgerRepository;
        this.jobRunner = jobRunner;
        this.glPeriodRepository = glPeriodRepository;
        this.featureFlagService = featureFlagService;
        this.quicksightExportService = quicksightExportService;

    }

    @Timed
    public void runDailyGeneralLedgerHistoryPopulation() {
        String tenant = TenantContext.getCurrentTenant();
        log.info("Daily Job - General Ledger - Population - Start for Tenant : " + tenant);
        try {
            generalLedgerRepository.truncateGeneralLedgerTable();
        } catch (Exception e) {
            String message = "\nError: Query exception on tenant, " + tenant + ", for query:\n\n" + truncateLedgerTableForHistoryPull + "\n\n";
            log.error(e.getMessage() + message);
        }

        try {
            insertGLCategories();
            log.info("Daily Job - General Ledger - Population - Complete for Tenant : " + tenant);
        } catch (Exception e) {
            String message = "\nGeneral Ledger Population Error: Query exception on tenant, " + tenant + ", for query:\n\n" + gLImportSQL + "\n\n";
            log.error(e.getMessage() + message);
            log.info("Daily Job - General Ledger - Population - Failure for Tenant : " + tenant);
        }
    }

    @Timed
    public void runDailyLiveGeneralLedgerPopulation() {
        String tenant = TenantContext.getCurrentTenant();
        log.info("Daily Live GL Population Start for Tenant : " + tenant);
//        try {
//            generalLedgerRepository.deleteGeneralLedgerSales();
//            generalLedgerRepository.populateLiveGeneralLedgerSales();
//            log.info("Daily Job - Live GL Population Sales - Complete for Tenant : " + tenant);
//        } catch (Exception e) {
//            String message = "\nLive General Ledger Sales Population - Error for Tenant : " + tenant;
//            log.error(e.getMessage() + message);
//            log.info("Daily Live GL Sales Population - Failure for Tenant : " + tenant);
//        }
//
//        try {
//            generalLedgerRepository.deleteGeneralLedgerPayments();
//            generalLedgerRepository.populateLiveGeneralLedgerPayments();
//            log.info("Daily Job - Live GL Population Payments - Complete for Tenant : " + tenant);
//        } catch (Exception e) {
//            String message = "\nLive General Ledger Payments Population - Error for Tenant : " + tenant;
//            log.error(e.getMessage() + message);
//            log.info("Daily Live GL Payments Population - Failure for Tenant : " + tenant);
//        }
//
//        try {
//            generalLedgerRepository.deleteGeneralLedgerLineAdjustments();
//            generalLedgerRepository.populateLiveGeneralLedgerLineAdjustments();
//            log.info("Daily Job - Live GL Population Line Adjustments - Complete for Tenant : " + tenant);
//        } catch (Exception e) {
//            String message = "\nLive General Ledger Line Adjustments Population - Error for Tenant : " + tenant;
//            log.error(e.getMessage() + message);
//            log.info("Daily Live GL Line Adjustments Population - Failure for Tenant : " + tenant);
//        }
//
//        try {
//            generalLedgerRepository.deleteGeneralLedgerArAdjustments();
//            generalLedgerRepository.populateLiveGeneralLedgerArAdjustments();
//            log.info("Daily Job - Live GL Population AR Adjustments - Complete for Tenant : " + tenant);
//        } catch (Exception e) {
//            String message = "\nLive General Ledger AR Adjustments Population - Error for Tenant : " + tenant;
//            log.error(e.getMessage() + message);
//            log.info("Daily Live GL AR Adjustments Population - Failure for Tenant : " + tenant);
//        }

    }

    @Timed
    public void triggerGLToAws() {
        //Begin AWS Upload
        log.info("Daily AWS Upload Job - General Ledger - Upload Job - Start for Tenant : " + TenantContext.getCurrentTenant());
        JobExecution execution = null;
        try {
            execution = jobRunner.runAWSGeneralLedgerUploadJob();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            log.info("Daily AWS Upload Job - General Ledger - Upload Job - End for Tenant : " + TenantContext.getCurrentTenant() + " Execution Status: " + execution.getStatus());
        }
    }

    public void importGeneralLedgerHistoryToMultitenant() {
        String targetPath = uploadDirectory + "/quicksight/" + TenantContext.getCurrentTenant() + "GeneralLedgerImport.sql";
        try {
//            writeBulkInsertStatementGeneralLedger(targetPath);
//            importQuicksight(targetPath, url.split("jdbc:mysql://")[1].split("/")[0].split(":")[0]);
            importMultitenantGeneralLedgerData();
            //Begin AWS Upload
            //triggerGLToAws();
        } catch (Exception e) {
            log.error(e.getMessage() + "\nError: Updating QS General Ledger for tenant : " + TenantContext.getCurrentTenant() + "\n");
        }
    }

    @Timed
    public void importQuicksight(String sqlFile, String mysqlHost) {
        log.info("Daily Job - General Ledger - Insert Multitenant Start : " + TenantContext.getCurrentTenant());
        ProcessBuilder processBuilder = new ProcessBuilder();
        String defaultsFileHolder = StringUtil.getMySSQLConfigurationFile(activeProfiles);
        processBuilder.command("bash", "-c", "mysql".concat(defaultsFileHolder).concat("-h ".concat(mysqlHost).concat(" multitenant < ").concat(sqlFile)));
//        processBuilder.command("bash", "-c", "docker exec -i mysql57 mysql".concat(defaultsFileHolder).concat("-h ".concat(mysqlHost).concat(" multitenant < ").concat(sqlFile)));
        try {
            String contents = FileUtils.readFileToString(new File(sqlFile), "UTF-8");
            if (contents.trim().endsWith("VALUES")) {
                log.info("No General Ledger to import from ".concat(sqlFile));
                return;
            }

            Process process = processBuilder.start();
            StringBuilder output = new StringBuilder();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            int exitVal = process.waitFor();
            if (exitVal == 0) {
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            } else {
                reader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
                log.error(String.valueOf(output));
            }
        } catch (Exception ex) {
            log.error("Daily Job - General Ledger - Error " + ex.getMessage() + "\n" + ex.getLocalizedMessage());
        }
        log.info("Daily Job - General Ledger - Insert Multitenant Complete : " + TenantContext.getCurrentTenant());
    }

    @Timed
    public void writeBulkInsertStatementGeneralLedger(String targetPath) {
        String message = "Daily Job - General Ledger - Write Bulk Insert File Start : %s -> %s";
        log.info(String.format(message, TenantContext.getCurrentTenant(), targetPath));
        File sqlFile;
        OutputStream fileOutStream = null;
        try {
            sqlFile = new File(targetPath);
            if (!sqlFile.exists()) {
                sqlFile = Files.createFile(sqlFile.toPath()).toFile();
            }
            fileOutStream = new FileOutputStream(sqlFile, false);
            List<Object[]> data = generalLedgerRepository.getAllLedgerEntriesWithMultitenantData();
            String delete = "DELETE FROM multitenant.general_ledger WHERE tenant='" + TenantContext.getCurrentTenant() + "';\n";
            fileOutStream.write(delete.getBytes(StandardCharsets.UTF_8));
            String insertHeader = "INSERT INTO multitenant.general_ledger(id,tenant,category_id,gl_date,gl_applied_date,gl_year,gl_period,gl_account,category,\n" +
                    "sub_category,amount,abs_amount,patient_id,patient,prescription_id,claim_id,branch_id,branch, patient_branch_id,patient_branch, prescription_branch_id,prescription_branch, facility_id,facility,prescription_l_code_id,\n" +
                    "l_code_id,l_code,insurance_verification_id,insurance_verification_l_code_id,payment_id,applied_payment_id,applied_payment_l_code_id,payer_type,\n" +
                    "payment_type, check_number,insurance_company_id,insurance_company,carrier_type,patient_insurance_id,device_type_id,device_type_category,device_type,\n" +
                    "treating_practitioner_id,treating_practitioner,primary_care_physician_id,primary_care_physician,therapist_id,therapist,\n" +
                    "referring_physician_id,referring_physician,claim_submission_date,date_of_service,prescription_date,payment_date,deposit_date,applied_date,\n" +
                    "rx_active,patient_active,last_updated) VALUES \n";
            fileOutStream.write(insertHeader.getBytes(StandardCharsets.UTF_8));
            int row = 1;
            int lastIndex = data.size();
            for (Object[] i : data) {
                int index = row++;
                boolean isBatch = index % 5000 == 0;
                String line = writeMultitenantInsertStatementLine(i, (lastIndex == index || isBatch));
                fileOutStream.write(line.getBytes(StandardCharsets.UTF_8));
                fileOutStream.flush();
                if (isBatch) {
                    fileOutStream.write(insertHeader.getBytes(StandardCharsets.UTF_8));
                    fileOutStream.flush();
                }
            }
            log.info("Daily Job - General Ledger - Write Bulk Insert File Finish : " + TenantContext.getCurrentTenant());
        } catch (IOException e) {
            log.error(e.getMessage() + " Writing SQL Bulk Insert File Failed. I/O Exception");
        } catch (Exception e) {
            log.error(e.getMessage() + " Writing SQL Bulk Insert File Failed. General Exception");
        } finally {
            // because I must
            try {
                if (fileOutStream != null) {
                    fileOutStream.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage() + " Finally Block while writing SQL Insert File Failed");
            }
        }
    }

    /**
     * @param i GeneralLedgerFields (Interface)
     * @return String
     * Returning the toString() to allow for GC since StringBuilder
     * is prone to memory hog and leaks.
     * <p>
     * The order to writing this is very important and should maintain the
     * order of the header string from writeBulkInsertStatementGeneralLedger()
     */
    public String writeMultitenantInsertStatementLine(Object[] i, boolean isLast) {
        String result;
        StringBuilder builder = new StringBuilder();
        String quotes = "\'";
        builder.append("(null, ");
        builder.append(quotes).append(TenantContext.getCurrentTenant()).append(quotes).append(", ");
        builder.append(i[GeneralLedgerFields.categoryId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.glDate.ordinal()] != null ? quotes + i[GeneralLedgerFields.glDate.ordinal()] + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.glAppliedDate.ordinal()] != null ? quotes + i[GeneralLedgerFields.glAppliedDate.ordinal()] + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.year.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.period.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.glAccount.ordinal()] != null ? quotes + i[GeneralLedgerFields.glAccount.ordinal()] + quotes : "''").append(", ");
        builder.append(quotes).append(i[GeneralLedgerFields.category.ordinal()]).append(quotes).append(", ");
        builder.append(quotes).append(StringEscapeUtils.escapeSql(String.valueOf(i[GeneralLedgerFields.subCategory.ordinal()]))).append(quotes).append(", ");
        builder.append(i[GeneralLedgerFields.amount.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.absAmount.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.patientId.ordinal()]).append(", ");
        FullName fn = new FullName(i[GeneralLedgerFields.ptFirstName.ordinal()], i[GeneralLedgerFields.ptMiddleName.ordinal()], i[GeneralLedgerFields.ptLastName.ordinal()]);
        builder.append(i[GeneralLedgerFields.patientId.ordinal()] != null ? quotes + StringEscapeUtils.escapeSql(StringUtil.formatName(fn, "FML", false)) + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.prescriptionId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.claimId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.branchId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.branch.ordinal()] != null ? quotes + StringEscapeUtils.escapeSql(i[GeneralLedgerFields.branch.ordinal()].toString()) + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.patientBranchId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.patientBranch.ordinal()] != null ? quotes + StringEscapeUtils.escapeSql(i[GeneralLedgerFields.patientBranch.ordinal()].toString()) + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.prescriptionBranchId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.prescriptionBranch.ordinal()] != null ? quotes + StringEscapeUtils.escapeSql(i[GeneralLedgerFields.prescriptionBranch.ordinal()].toString()) + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.facilityId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.facility.ordinal()] != null ? quotes + StringEscapeUtils.escapeSql(i[GeneralLedgerFields.facility.ordinal()].toString()) + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.prescriptionLCodeId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.lCodeId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.lCode.ordinal()] != null ? quotes + i[GeneralLedgerFields.lCode.ordinal()] + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.insuranceVerificationId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.insuranceVerificationLCodeId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.paymentId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.appliedPaymentId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.appliedPaymentLCodeId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.payerType.ordinal()] != null ? quotes + i[GeneralLedgerFields.payerType.ordinal()] + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.paymentType.ordinal()] != null ? quotes + i[GeneralLedgerFields.paymentType.ordinal()] + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.checkNumber.ordinal()] != null ? quotes + StringEscapeUtils.escapeSql(i[GeneralLedgerFields.checkNumber.ordinal()].toString()) + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.insuranceCompanyId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.insuranceCompany.ordinal()] != null ? quotes + StringEscapeUtils.escapeSql(i[GeneralLedgerFields.insuranceCompany.ordinal()].toString()) + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.carrierType.ordinal()] != null ? quotes + i[GeneralLedgerFields.carrierType.ordinal()] + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.patientInsuranceId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.deviceTypeId.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.deviceTypeCategory.ordinal()] != null ? quotes + i[GeneralLedgerFields.deviceTypeCategory.ordinal()] + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.deviceType.ordinal()] != null ? quotes + StringEscapeUtils.escapeSql(i[GeneralLedgerFields.deviceType.ordinal()].toString()) + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.treatingPractitionerId.ordinal()]).append(", ");
        fn = new FullName(i[GeneralLedgerFields.tpFirstName.ordinal()], i[GeneralLedgerFields.tpMiddleName.ordinal()], i[GeneralLedgerFields.tpLastName.ordinal()], i[GeneralLedgerFields.tpCredentials.ordinal()]);
        builder.append(i[GeneralLedgerFields.treatingPractitionerId.ordinal()] != null ? quotes + StringEscapeUtils.escapeSql(StringUtil.formatName(fn, "FMFC", false)) + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.primaryCarePhysicianId.ordinal()]).append(", ");
        fn = new FullName(i[GeneralLedgerFields.pcFirstName.ordinal()], i[GeneralLedgerFields.pcMiddleName.ordinal()], i[GeneralLedgerFields.pcLastName.ordinal()], i[GeneralLedgerFields.pcCredentials.ordinal()]);
        builder.append(i[GeneralLedgerFields.primaryCarePhysicianId.ordinal()] != null ? quotes + StringEscapeUtils.escapeSql(StringUtil.formatName(fn, "FMFC", false)) + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.therapistId.ordinal()]).append(", ");
        fn = new FullName(i[GeneralLedgerFields.thFirstName.ordinal()], i[GeneralLedgerFields.thMiddleName.ordinal()], i[GeneralLedgerFields.thLastName.ordinal()], i[GeneralLedgerFields.thCredentials.ordinal()]);
        builder.append(i[GeneralLedgerFields.therapistId.ordinal()] != null ? quotes + StringEscapeUtils.escapeSql(StringUtil.formatName(fn, "FMFC", false)) + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.referringPhysicianId.ordinal()]).append(", ");
        fn = new FullName(i[GeneralLedgerFields.rpFirstName.ordinal()], i[GeneralLedgerFields.rpMiddleName.ordinal()], i[GeneralLedgerFields.rpLastName.ordinal()], i[GeneralLedgerFields.rpCredentials.ordinal()]);
        builder.append(i[GeneralLedgerFields.referringPhysicianId.ordinal()] != null ? quotes + StringEscapeUtils.escapeSql(StringUtil.formatName(fn, "", false)) + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.claimSubmissionDate.ordinal()] != null ? quotes + i[GeneralLedgerFields.claimSubmissionDate.ordinal()] + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.dateOfService.ordinal()] != null ? quotes + i[GeneralLedgerFields.dateOfService.ordinal()] + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.prescriptionDate.ordinal()] != null ? quotes + i[GeneralLedgerFields.prescriptionDate.ordinal()] + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.paymentDate.ordinal()] != null ? quotes + i[GeneralLedgerFields.paymentDate.ordinal()] + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.depositDate.ordinal()] != null ? quotes + i[GeneralLedgerFields.depositDate.ordinal()] + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.appliedDate.ordinal()] != null ? quotes + i[GeneralLedgerFields.appliedDate.ordinal()] + quotes : null).append(", ");
        builder.append(i[GeneralLedgerFields.rxActive.ordinal()]).append(", ");
        builder.append(i[GeneralLedgerFields.ptActive.ordinal()]).append(", ");
        builder.append((i[GeneralLedgerFields.lastUpdated.ordinal()] != null ? quotes + i[GeneralLedgerFields.lastUpdated.ordinal()] + quotes : null));
        builder.append((isLast ? ");\n" : "),\n"));
        result = builder.toString();
        // did this due to heap issue error. Not specifically for this that I know of
        // even though toString should remove references I am going to double make sure
        // GC works properly by setting builder to null saying Java take it all back.
        builder = null;
        return result;
    }

    public void runStaticGeneralLedgerPopulationByPeriod(GL_Period period) {
        try {
            generalLedgerRepository.populateStaticGeneralLedgerByPeriod(period.getYear(), period.getPeriod());
            log.info("Daily Job - General Ledger Static - Success for Tenant: " + TenantContext.getCurrentTenant() + " -> Year: [" + period.getYear() + "] and Period: [" + period.getPeriod() + "]");
        } catch (Exception e) {
            log.info("Daily Job - General Ledger Static - Import - Failure for Tenant : " + TenantContext.getCurrentTenant() + " -> Year: [" + period.getYear() + "] and Period: [" + period.getPeriod() + "]");
            log.error("Daily Job - General Ledger Static - Import - Error : " + e.getMessage());
        }
    }

    @Timed
    public void executeStaticGeneralLedgerProcess() {
        List<GL_Period> glPeriods = glPeriodRepository.findAllByStatusEquals("closed");
        if (glPeriods != null && !glPeriods.isEmpty()) {
            log.info("Daily Static GL Job Start - " + TenantContext.getCurrentTenant());
            for (GL_Period period : glPeriods) {
                boolean isPresent = isStaticGeneralLedgerEntryPresentForYearAndPeriod(period.getYear(), period.getPeriod());
                if (!isPresent) {
                    runStaticGeneralLedgerPopulationByPeriod(period);
                }
            }
            log.info("Daily Static GL Job Finish - " + TenantContext.getCurrentTenant());
        }
    }

    public boolean isStaticGeneralLedgerEntryPresentForYearAndPeriod(Long year, Long period) {
        BigInteger temp = generalLedgerRepository.isStaticGeneralLedgerEntryPresentByYearAndPeriod(year, period);
        return temp.compareTo(BigInteger.ONE) == 0;
    }

    public void setAllStaticTransactionsActiveEqualsFalseForPeriod(Long year, Long period) {
        generalLedgerRepository.markStaticGeneralLedgerEntriesFalseByPeriod(year, period);
    }

    public boolean checkIfTransactionIsClosed(LocalDate glDate, LocalDate glApplied) {
        GL_Period result = glPeriodRepository.findGLPeriodTransactionBelongsToLocalDate(glDate, glApplied);
        return result.getClosedDate() != null;
    }

    public List<GeneralLedger> findByClaimId(Long claimId) {
        return generalLedgerRepository.findByClaimId(claimId);
    }

    public List<GeneralLedger> findByClaimIdIn(List<Long> claimIds) {
        return generalLedgerRepository.findByClaimIdIn(claimIds);
    }

    public List<GeneralLedger> findAllGeneralLedgerTrasactions() {
        return generalLedgerRepository.findAllGeneralLedgerTransactions();
    }

    public List<GeneralLedgerUnion> getGeneralLedgerUnionTransactions() {
        return generalLedgerRepository.getGeneralLedgerUnionTransactions();
    }

    @Timed
    public void importMultitenantGeneralLedgerData() {
        String tenant = TenantContext.getCurrentTenant();

        generalLedgerRepository.removeStaleTenantData(tenant);

        List<Object[]> data = generalLedgerRepository.getAllLedgerEntriesWithMultitenantData();
        int rows = data.size();
        log.info("Importing {} rows to multitenant.general_ledger for {}", rows, tenant);
        int row = 1;
        for (Object[] i : data) {
            FullName patient = new FullName(i[GeneralLedgerFields.ptFirstName.ordinal()], i[GeneralLedgerFields.ptMiddleName.ordinal()], i[GeneralLedgerFields.ptLastName.ordinal()]);
            FullName treatingPractitioner = new FullName(i[GeneralLedgerFields.tpFirstName.ordinal()], i[GeneralLedgerFields.tpMiddleName.ordinal()], i[GeneralLedgerFields.tpLastName.ordinal()], i[GeneralLedgerFields.tpCredentials.ordinal()]);
            FullName primaryCarePhysician = new FullName(i[GeneralLedgerFields.pcFirstName.ordinal()], i[GeneralLedgerFields.pcMiddleName.ordinal()], i[GeneralLedgerFields.pcLastName.ordinal()], i[GeneralLedgerFields.pcCredentials.ordinal()]);
            FullName therapist = new FullName(i[GeneralLedgerFields.thFirstName.ordinal()], i[GeneralLedgerFields.thMiddleName.ordinal()], i[GeneralLedgerFields.thLastName.ordinal()], i[GeneralLedgerFields.thCredentials.ordinal()]);
            FullName referringPhysician = new FullName(i[GeneralLedgerFields.rpFirstName.ordinal()], i[GeneralLedgerFields.rpMiddleName.ordinal()], i[GeneralLedgerFields.rpLastName.ordinal()], i[GeneralLedgerFields.rpCredentials.ordinal()]);

            try {
                generalLedgerRepository.importGeneralLedgerData(tenant,
                        Long.valueOf(String.valueOf(i[GeneralLedgerFields.categoryId.ordinal()])),
                        i[GeneralLedgerFields.glDate.ordinal()] == null ? null : Date.valueOf(String.valueOf(i[GeneralLedgerFields.glDate.ordinal()])),
                        i[GeneralLedgerFields.glAppliedDate.ordinal()] == null ? null : Date.valueOf(String.valueOf(i[GeneralLedgerFields.glAppliedDate.ordinal()])),
                        i[GeneralLedgerFields.year.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.year.ordinal()])),
                        i[GeneralLedgerFields.period.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.period.ordinal()])),
                        i[GeneralLedgerFields.glAccount.ordinal()] == null ? "" : String.valueOf(i[GeneralLedgerFields.glAccount.ordinal()]),
                        i[GeneralLedgerFields.category.ordinal()] == null ? "" : String.valueOf(i[GeneralLedgerFields.category.ordinal()]),
                        i[GeneralLedgerFields.subCategory.ordinal()] == null ? "" : StringEscapeUtils.escapeSql(String.valueOf(i[GeneralLedgerFields.subCategory.ordinal()])),
                        i[GeneralLedgerFields.amount.ordinal()] == null ? BigDecimal.ZERO : new BigDecimal(String.valueOf(i[GeneralLedgerFields.amount.ordinal()])),
                        i[GeneralLedgerFields.absAmount.ordinal()] == null ? BigDecimal.ZERO : new BigDecimal(String.valueOf(i[GeneralLedgerFields.absAmount.ordinal()])),
                        i[GeneralLedgerFields.patientId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.patientId.ordinal()])),
                        i[GeneralLedgerFields.patientId.ordinal()] == null ? null : StringEscapeUtils.escapeSql(StringUtil.formatName(patient, "FML", false)),
                        i[GeneralLedgerFields.prescriptionId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.prescriptionId.ordinal()])),
                        i[GeneralLedgerFields.claimId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.claimId.ordinal()])),
                        i[GeneralLedgerFields.branchId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.branchId.ordinal()])),
                        i[GeneralLedgerFields.branch.ordinal()] == null ? null : StringEscapeUtils.escapeSql(i[GeneralLedgerFields.branch.ordinal()].toString()),
                        i[GeneralLedgerFields.patientBranchId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.patientBranchId.ordinal()])),
                        i[GeneralLedgerFields.patientBranch.ordinal()] == null ? null : StringEscapeUtils.escapeSql(i[GeneralLedgerFields.patientBranch.ordinal()].toString()),
                        i[GeneralLedgerFields.prescriptionBranchId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.prescriptionBranchId.ordinal()])),
                        i[GeneralLedgerFields.prescriptionBranch.ordinal()] == null ? null : StringEscapeUtils.escapeSql(i[GeneralLedgerFields.prescriptionBranch.ordinal()].toString()),
                        i[GeneralLedgerFields.facilityId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.facilityId.ordinal()])),
                        i[GeneralLedgerFields.facility.ordinal()] == null ? null : StringEscapeUtils.escapeSql(i[GeneralLedgerFields.facility.ordinal()].toString()),
                        i[GeneralLedgerFields.prescriptionLCodeId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.prescriptionLCodeId.ordinal()])),
                        i[GeneralLedgerFields.lCodeId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.lCodeId.ordinal()])),
                        i[GeneralLedgerFields.lCode.ordinal()] == null ? null : String.valueOf(i[GeneralLedgerFields.lCode.ordinal()]),
                        i[GeneralLedgerFields.insuranceVerificationId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.insuranceVerificationId.ordinal()])),
                        i[GeneralLedgerFields.insuranceVerificationLCodeId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.insuranceVerificationLCodeId.ordinal()])),
                        i[GeneralLedgerFields.paymentId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.paymentId.ordinal()])),
                        i[GeneralLedgerFields.appliedPaymentId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.appliedPaymentId.ordinal()])),
                        i[GeneralLedgerFields.appliedPaymentLCodeId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.appliedPaymentLCodeId.ordinal()])),
                        i[GeneralLedgerFields.payerType.ordinal()] == null ? null : String.valueOf(i[GeneralLedgerFields.payerType.ordinal()]),
                        i[GeneralLedgerFields.paymentType.ordinal()] == null ? null : String.valueOf(i[GeneralLedgerFields.paymentType.ordinal()]),
                        i[GeneralLedgerFields.checkNumber.ordinal()] == null ? null : StringEscapeUtils.escapeSql(i[GeneralLedgerFields.checkNumber.ordinal()].toString()),
                        i[GeneralLedgerFields.insuranceCompanyId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.insuranceCompanyId.ordinal()])),
                        i[GeneralLedgerFields.insuranceCompany.ordinal()] == null ? null : StringEscapeUtils.escapeSql(i[GeneralLedgerFields.insuranceCompany.ordinal()].toString()),
                        i[GeneralLedgerFields.carrierType.ordinal()] == null ? null : String.valueOf(i[GeneralLedgerFields.carrierType.ordinal()]),
                        i[GeneralLedgerFields.patientInsuranceId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.patientInsuranceId.ordinal()])),
                        i[GeneralLedgerFields.deviceTypeId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.deviceTypeId.ordinal()])),
                        i[GeneralLedgerFields.deviceTypeCategory.ordinal()] == null ? null : String.valueOf(i[GeneralLedgerFields.deviceTypeCategory.ordinal()]),
                        i[GeneralLedgerFields.deviceType.ordinal()] == null ? null : StringEscapeUtils.escapeSql(i[GeneralLedgerFields.deviceType.ordinal()].toString()),
                        i[GeneralLedgerFields.treatingPractitionerId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.treatingPractitionerId.ordinal()])),
                        i[GeneralLedgerFields.treatingPractitionerId.ordinal()] == null ? null : StringEscapeUtils.escapeSql(StringUtil.formatName(treatingPractitioner, "FMFC", false)),
                        i[GeneralLedgerFields.primaryCarePhysicianId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.primaryCarePhysicianId.ordinal()])),
                        i[GeneralLedgerFields.primaryCarePhysicianId.ordinal()] == null ? null : StringEscapeUtils.escapeSql(StringUtil.formatName(primaryCarePhysician, "FMFC", false)),
                        i[GeneralLedgerFields.therapistId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.therapistId.ordinal()])),
                        i[GeneralLedgerFields.therapistId.ordinal()] == null ? null : StringEscapeUtils.escapeSql(StringUtil.formatName(therapist, "FMFC", false)),
                        i[GeneralLedgerFields.referringPhysicianId.ordinal()] == null ? null : Long.valueOf(String.valueOf(i[GeneralLedgerFields.referringPhysicianId.ordinal()])),
                        i[GeneralLedgerFields.referringPhysicianId.ordinal()] == null ? null : StringEscapeUtils.escapeSql(StringUtil.formatName(referringPhysician, "", false)),
                        i[GeneralLedgerFields.claimSubmissionDate.ordinal()] == null ? null : Date.valueOf(String.valueOf(i[GeneralLedgerFields.claimSubmissionDate.ordinal()])),
                        i[GeneralLedgerFields.dateOfService.ordinal()] == null ? null : Date.valueOf(String.valueOf(i[GeneralLedgerFields.dateOfService.ordinal()])),
                        i[GeneralLedgerFields.prescriptionDate.ordinal()] == null ? null : Date.valueOf(String.valueOf(i[GeneralLedgerFields.prescriptionDate.ordinal()])),
                        i[GeneralLedgerFields.paymentDate.ordinal()] == null ? null : Date.valueOf(String.valueOf(i[GeneralLedgerFields.paymentDate.ordinal()])),
                        i[GeneralLedgerFields.depositDate.ordinal()] == null ? null : Date.valueOf(String.valueOf(i[GeneralLedgerFields.depositDate.ordinal()])),
                        i[GeneralLedgerFields.appliedDate.ordinal()] == null ? null : Date.valueOf(String.valueOf(i[GeneralLedgerFields.appliedDate.ordinal()])),
                        i[GeneralLedgerFields.rxActive.ordinal()] == null ? Boolean.FALSE : Boolean.valueOf(String.valueOf(i[GeneralLedgerFields.rxActive.ordinal()])),
                        i[GeneralLedgerFields.ptActive.ordinal()] == null ? Boolean.FALSE : Boolean.valueOf(String.valueOf(i[GeneralLedgerFields.ptActive.ordinal()])),
                        i[GeneralLedgerFields.lastUpdated.ordinal()] == null ? null : Timestamp.valueOf(String.valueOf(i[GeneralLedgerFields.lastUpdated.ordinal()]))
                );
            } catch (Exception e) {
                log.error("Error importing row {}, patient id {}, prescription id {} cause : {}", row, i[GeneralLedgerFields.patientId.ordinal()], i[GeneralLedgerFields.prescriptionId.ordinal()], e.getMessage());
            }
            if ((row > 0 && row % 1000 == 0) || row == rows) {
                log.info("Imported {} rows of {} total rows", row, rows);
            }
            row++;
        }
    }

    public void insertGLCategories() {
        int total = 0;
        String tenant = TenantContext.getCurrentTenant();
        Integer c1 = generalLedgerRepository.importGeneralLedgerHistoryC1();
        String msg = "GL Category {} " + tenant + " insert count: {}";
        String msgTotal = "GL Total       " + tenant + " insert count: {}";

        log.info(msg, " 1", c1);
        total = total + c1;
        Integer c2 = generalLedgerRepository.importGeneralLedgerHistoryC2();
        log.info(msg, " 2", c2);
        total = total + c2;
        Integer c3 = generalLedgerRepository.importGeneralLedgerHistoryC3();
        log.info(msg, " 3", c3);
        total = total + c3;
        Integer c4 = generalLedgerRepository.importGeneralLedgerHistoryC4();
        log.info(msg, " 4", c4);
        total = total + c4;
        Integer c5 = generalLedgerRepository.importGeneralLedgerHistoryC5();
        log.info(msg, " 5", c5);
        total = total + c5;
        Integer c6 = generalLedgerRepository.importGeneralLedgerHistoryC6();
        log.info(msg, " 6", c6);
        total = total + c6;
        Integer c7 = generalLedgerRepository.importGeneralLedgerHistoryC7();
        log.info(msg, " 7", c7);
        total = total + c7;
        Integer c8 = generalLedgerRepository.importGeneralLedgerHistoryC8();
        log.info(msg, " 8", c8);
        total = total + c8;
        Integer c9 = generalLedgerRepository.importGeneralLedgerHistoryC9();
        log.info(msg, " 9", c9);
        total = total + c9;
        Integer c10 = generalLedgerRepository.importGeneralLedgerHistoryC10();
        log.info(msg, "10", c10);
        total = total + c10;
        Integer c11 = generalLedgerRepository.importGeneralLedgerHistoryC11();
        log.info(msg, "11", c11);
        total = total + c11;
        Integer c12 = generalLedgerRepository.importGeneralLedgerHistoryC12();
        log.info(msg, "12", c12);
        total = total + c12;
        Integer c13 = generalLedgerRepository.importGeneralLedgerHistoryC13();
        log.info(msg, "13", c13);
        total = total + c13;
        Integer c14 = generalLedgerRepository.importGeneralLedgerHistoryC14();
        log.info(msg, "14", c14);
        total = total + c14;
        Integer c15 = generalLedgerRepository.importGeneralLedgerHistoryC15();
        log.info(msg, "15", c15);
        total = total + c15;
        Integer c16 = generalLedgerRepository.importGeneralLedgerHistoryC16();
        log.info(msg, "16", c16);
        total = total + c16;
        Integer c17 = generalLedgerRepository.importGeneralLedgerHistoryC17();
        log.info(msg, "17", c17);
        total = total + c17;
        Integer c18 = generalLedgerRepository.importGeneralLedgerHistoryC18();
        log.info(msg, "18", c18);
        total = total + c18;
        Integer c19 = generalLedgerRepository.importGeneralLedgerHistoryC19();
        log.info(msg, "19", c19);
        total = total + c19;
        Integer c20 = generalLedgerRepository.importGeneralLedgerHistoryC20();
        log.info(msg, "20", c20);
        total = total + c20;
        Integer c21 = generalLedgerRepository.importGeneralLedgerHistoryC21();
        log.info(msg, "21", c21);
        total = total + c21;
        Integer c22 = generalLedgerRepository.importGeneralLedgerHistoryC22();
        log.info(msg, "22", c22);
        total = total + c22;
        Integer c23 = generalLedgerRepository.importGeneralLedgerHistoryC23();
        log.info(msg, "23", c23);
        total = total + c23;
        Integer c24 = generalLedgerRepository.importGeneralLedgerHistoryC24();
        total = total + c24;
        log.info(msg, "24", c24);
        log.info(msgTotal, total);
    }
    public List<GeneralLedger> findAllByPaymentId(Long paymentId){
        List<GeneralLedger> result = generalLedgerRepository.findAllByPaymentId(paymentId);
        return result;
    }

    public void dailyReload(boolean hasQuicksight) {
        try {
            runDailyGeneralLedgerHistoryPopulation();
            executeStaticGeneralLedgerProcess();
            if (hasQuicksight) {
                quicksightExportService.uploadGeneralLedgerCsvS3Quicksight();
            }
            if (featureFlagService.findFeatureFlagByFeature("live_gl") != null) {
                runDailyLiveGeneralLedgerPopulation();
            }
            if (featureFlagService.findFeatureFlagByFeature("upload_general_ledger_aws_s3") != null) {
                importGeneralLedgerHistoryToMultitenant();
                triggerGLToAws();
            }
        } catch (Exception e) {
            log.error(StringUtil.getExceptionAsString(e));
        }
    }

    @Override
    public void loadForeignKeys(GeneralLedger object) {
        //Eat it (No need for any execution at this  time)
    }
}
