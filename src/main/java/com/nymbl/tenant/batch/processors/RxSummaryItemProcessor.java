package com.nymbl.tenant.batch.processors;

import com.nymbl.tenant.model.ExpandedRxSummary;
import org.apache.commons.lang.StringUtils;
import org.springframework.batch.item.ItemProcessor;

public class RxSummaryItemProcessor implements ItemProcessor<ExpandedRxSummary, ExpandedRxSummary> {

	public ExpandedRxSummary process(ExpandedRxSummary item) {

		if (StringUtils.isNotBlank(item.getPatientName())) {
			item.setPatientName("\"" + item.getPatientName() + "\"");
		}
		if (StringUtils.isNotBlank(item.getCreatedByName())) {
			item.setCreatedByName("\"" + item.getCreatedByName() + "\"");
		}
		if (StringUtils.isNotBlank(item.getDeviceName())) {
			item.setDeviceName("\"" + item.getDeviceName() + "\"");
		}
		if (StringUtils.isNotBlank(item.getDeviceType())) {
			item.setDeviceType("\"" + item.getDeviceType() + "\"");
		}
		if (StringUtils.isNotBlank(item.getTreatingPractitionerName())) {
			item.setTreatingPractitionerName("\"" + item.getTreatingPractitionerName() + "\"");
		}
		if (StringUtils.isNotBlank(item.getReferringPhysicianName())) {
			item.setReferringPhysicianName("\"" + item.getReferringPhysicianName() + "\"");
		}
		if (StringUtils.isNotBlank(item.getResidentName())) {
			item.setResidentName("\"" + item.getResidentName() + "\"");
		}
		if (StringUtils.isNotBlank(item.getPrimaryCarePhysicianName())) {
			item.setPrimaryCarePhysicianName("\"" + item.getPrimaryCarePhysicianName() + "\"");
		}
		if (StringUtils.isNotBlank(item.getClericalUserName())) {
			item.setClericalUserName("\"" + item.getClericalUserName() + "\"");
		}
		if (StringUtils.isNotBlank(item.getFacilityName())) {
			item.setFacilityName("\"" + item.getFacilityName() + "\"");
		}
		if (StringUtils.isNotBlank(item.getRxStatus())) {
			item.setRxStatus("\"" + item.getRxStatus() + "\"");
		}
		if (StringUtils.isNotBlank(item.getRxLCodes())) {
			item.setRxLCodes("\"" + item.getRxLCodes() + "\"");
		}
		if (StringUtils.isNotBlank(item.getInsurance())) {
			item.setInsurance("\"" + item.getInsurance() + "\"");
		}
		if (StringUtils.isNotBlank(item.getFabricationSteps())) {
			item.setFabricationSteps("\"" + item.getFabricationSteps() + "\"");
		}
		if (StringUtils.isNotBlank(item.getIncompleteSections())) {
			item.setIncompleteSections("\"" + item.getIncompleteSections() + "\"");
		}
		if (StringUtils.isNotBlank(item.getMissingDocuments())) {
			item.setResidentName("\"" + item.getResidentName() + "\"");
		}
		String latestComment = item.getLatestComment();
		if (latestComment != null) {
			if (latestComment.contains("\"")) {
				latestComment = latestComment.replace("\"", "''");
			}
			if (latestComment.length() > 32_750) {
				latestComment = latestComment.substring(0, 32_750);
			}

			final String newLine = System.lineSeparator();
			if (latestComment.contains(newLine)) {
				latestComment = latestComment.replace(newLine, "");
			}
			item.setLatestComment("\"" + latestComment + "\"");
		}
		if (StringUtils.isNotBlank(item.getLatestCommentBy())) {
			item.setLatestCommentBy("\"" + item.getLatestCommentBy() + "\"");
		}
		return item;
	}

}