package com.nymbl.tenant.repository;

import com.nymbl.tenant.model.GeneralLedger;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

public interface ClinicalOperationsRepository extends JpaRepository<GeneralLedger, Long>, JpaSpecificationExecutor<GeneralLedger> {


    @Modifying
    @Transactional
    @Query(value = removeStaleTenantData, nativeQuery = true)
    void removeStaleTenantData(@Param("tenant") String tenant);

    @Modifying
    @Transactional
    @Query(value = importTenantQuery, nativeQuery = true)
    void importClinicalOperationsData(@Param("tenant") String tenant);

    String removeStaleTenantData = "DELETE FROM `multitenant`.`clinical_operations` WHERE tenant = :tenant ; ";

    String importTenantQuery = "INSERT INTO `multitenant`.`clinical_operations`\n" +
            "( \n" +
            "`tenant`,\n" +
            "`patient_id`,\n" +
            "`patient`,\n" +
            "`patient_dob`,\n" +
            "`patient_created_at`,\n" +
            "`patient_created_by_id`,\n" +
            "`patient_created_by`,\n" +
            "`patient_primary_branch_id`,\n" +
            "`patient_primary_branch`,\n" +
            "`date_of_surgery`,\n" +
            "`prescription_id`,\n" +
            "`prescription_branch_id`,\n" +
            "`prescription_branch`,\n" +
            "`prescription_created_at`,\n" +
            "`prescription_duplicated_from_id`,\n" +
            "`prescription_created_by_id`,\n" +
            "`prescription_created_by`,\n" +
            "`prescription_assigned_to`,\n" +
            "`prescription_date`,\n" +
            "`prescription_projected_delivery_date`,\n" +
            "`prescription_delivered_on`,\n" +
            "`appointment_delivered_on`,\n" +
            "`prescription_delivery_location`,\n" +
            "`prescription_nymbl_status_id`,\n" +
            "`prescription_nymbl_status`,\n" +
            "`prescription_l_code_id`,\n" +
            "`l_code_id`,\n" +
            "`l_code`,\n" +
            "`quantity`,\n" +
            "`modifier1`,\n" +
            "`modifier2`,\n" +
            "`modifier3`,\n" +
            "`modifier4`,\n" +
            "`date_of_service`,\n" +
            "`pos`,\n" +
            "`device_type_id`,\n" +
            "`device_type_category`,\n" +
            "`device_type`,\n" +
            "`claim_id`,\n" +
            "`claim_date_of_service`,\n" +
            "`sent_to_billing_date`,\n" +
            "`date_resolved`,\n" +
            "`sent_to_billing_user`,\n" +
            "`claim_nymbl_status_id`,\n" +
            "`claim_nymbl_status`,\n" +
            "`claim_submission_date`,\n" +
            "`claim_billing_branch_id`,\n" +
            "`claim_billing_branch`,\n" +
            "`claim_branch`,\n" +
            "`claim_assigned_to`,\n" +
            "`total_cost`,\n" +
            "`total_applied_payment`,\n" +
            "`insurance_company_id`,\n" +
            "`insurance_company`,\n" +
            "`allowable_fee`,\n" +
            "`billing_fee`,\n" +
            "`covered`,\n" +
            "`bill`,\n" +
            "`total_allowable`,\n" +
            "`total_charge`,\n" +
            "`verified_by_id`,\n" +
            "`verified_by`,\n" +
            "`verified_on`,\n" +
            "`treating_practitioner_id`,\n" +
            "`treating_practitioner`,\n" +
            "`primary_care_physician_id`,\n" +
            "`primary_care_physician`,\n" +
            "`referring_physician_id`,\n" +
            "`referring_physician`,\n" +
            "`therapist_id`,\n" +
            "`therapist`,\n" +
            "`facility_id`,\n" +
            "`facility`,\n" +
            "`first_eval_date`,\n" +
            "`eval_created_by`,\n" +
            "`appointment_first_eval_date`,\n" +
            "`last_submission_by`,\n" +
            "`days_in_current_claim_status`,\n" +
            "`days_in_current_rx_status`,\n" +
            "`first_payment_date`)\n" +
            "SELECT DISTINCT \n" +
            ":tenant AS `tenant`,\n" +
            "`rx`.`patient_id` AS `patient_id`,\n" +
            "`nymbl_master`.`format_name`(`pt`.`first_name`, `pt`.`middle_name`, `pt`.`last_name`, NULL) AS `patient`,\n" +
            "`pt`.`dob` AS `patient_dob`,\n" +
            "`pt`.`created_at` AS `patient_created_at`,\n" +
            "`pt`.`created_by_id` AS `patient_created_by_id`,\n" +
            "`patu`.`username` AS `patient_created_by`,\n" +
            "`pt`.`primary_branch_id` AS `patient_primary_branch_id`,\n" +
            "`pb`.`name` AS `patient_primary_branch`,\n" +
            "`rx`.`surgery_date` AS `date_of_surgery`,\n" +
            "`rx`.`id` AS `prescription_id`,\n" +
            "`rx`.`branch_id` AS `prescription_branch_id`,\n" +
            "`rxb`.`name` AS `prescription_branch`,\n" +
            "`rx`.`created_at` AS `prescription_created_at`,\n" +
            "`rx`.`duplicated_from_id` AS `prescription_duplicated_from_id`,\n" +
            "`rx`.`created_by_id` AS `prescription_created_by_id`,\n" +
            "`rxu`.`username` AS `prescription_created_by`,\n" +
            "`clerical`.`username` AS `prescription_assigned_to`,\n" +
            "`rx`.`prescription_date` AS `prescription_date`,\n" +
            "`rx`.`projected_delivery_date` AS `prescription_projected_delivery_date`,\n" +
            "`rx`.`delivered_on` AS `prescription_delivered_on`,\n" +
            "COALESCE(MAX(`delivery_apt`.`start_datetime`), null) AS `appointment_delivered_on`,\t\n" +
            "`rx`.`delivery_location` AS `prescription_delivery_location`,\n" +
            "`rxns`.`id` AS `prescription_nymbl_status_id`,\n" +
            "`rxns`.`name` AS `prescription_nymbl_status`,\n" +
            "`plc`.`id` AS `prescription_l_code_id`,\n" +
            "`lc`.`id` AS `l_code_id`,\n" +
            "`lc`.`name` AS `l_code`,\n" +
            "`plc`.`quantity` AS `quantity`,\n" +
            "`plc`.`modifier1` AS `modifier1`,\n" +
            "`plc`.`modifier2` AS `modifier2`,\n" +
            "`plc`.`modifier3` AS `modifier3`,\n" +
            "`plc`.`modifier4` AS `modifier4`,\n" +
            "`plc`.`date_of_service` AS `l_code_date_of_service`,\n" +
            "`plc`.`pos` AS `l_code_place_of_service`,\n" +
            "`dt`.`id` AS `device_type_id`,\n" +
            "`dt`.`orthotic_or_prosthetic` AS `device_type_category`,\n" +
            "`dt`.`name` AS `device_name`,\n" +
            "`c`.`id` AS `claim_id`,\n" +
            "`c`.`date_of_service` AS `claim_date_of_service`,\n" +
            "`c`.`created_at` AS `sent_to_billing_date`,\n" +
            "`c`.`date_resolved` AS `date_resolved`,\n" +
            "`sb`.`username` AS `sent_to_billing_user`,\n" +
            "`c`.`nymbl_status_id` AS `claim_nymbl_status_id`,\n" +
            "`cns`.`name` AS `claim_nymbl_status`,\n" +
            "COALESCE(MIN(`cs`.`submission_date`), null) AS `claim_submission_date`,\n" +
            "`c`.`billing_branch_id` AS `claim_billing_branch_id`,\n" +
            "COALESCE(`cb`.`name`, '') AS `claim_billing_branch`,\n" +
            "COALESCE(`cb`.`name`, '') AS `claim_branch`,\n" +
            "`nymbl_master`.`format_name`(`c_user`.`first_name`, NULL, `c_user`.`last_name`, NULL) AS `claim_assigned_to`,\n" +
            "`poi_sum`.`total_cost`,\n" +
            "`aplc_sum`.`total_applied_payment`,\n" +
            "`ic`.`id` AS `insurance_company_id`,\n" +
            "`ic`.`name` AS `insurance_company`,\n" +
            "`ivlc`.`allowable_fee` AS `allowable_fee`,\n" +
            "`ivlc`.`billing_fee` AS `billing_fee`,\n" +
            "`ivlc`.`covered` AS `covered`,\n" +
            "`ivlc`.`bill` AS `bill`,\n" +
            "`ivlc`.`total_allowable` AS `total_allowable`,\n" +
            "`ivlc`.`total_charge` AS `total_charge`,\n" +
            "`iv`.`verified_by_id` AS `verified_by_id`,\n" +
            "`ivu`.`username` AS `verified_by`,\n" +
            "`iv`.`verified_on` AS `verified_on`,\n" +
            "`tp`.`id` AS `treating_practitioner_id`,\n" +
            "`nymbl_master`.`format_name`(`tp`.`first_name`, `tp`.`middle_name`, `tp`.`last_name`, NULL) AS `treating_practitioner`,\n" +
            "`pc`.`id` AS `primary_care_physician_id`,\n" +
            "`nymbl_master`.`format_name`(`tp`.`first_name`, `tp`.`middle_name`, `tp`.`last_name`, NULL) AS `primary_care_physician`,\n" +
            "`rp`.`id` AS `referring_physician_id`,\n" +
            "`nymbl_master`.`format_name`(`rp`.`first_name`, `rp`.`middle_name`, `rp`.`last_name`, NULL) AS `referring_physician`,\n" +
            "`th`.`id` AS `therapist_id`,\n" +
            "`nymbl_master`.`format_name`(`th`.`first_name`, `th`.`middle_name`, `th`.`last_name`, NULL) AS `therapist`,\n" +
            "`fac`.`id` AS `facility_id`,\n" +
            "`fac`.`name` AS `facility_name`,\n" +
            "MIN(COALESCE(`eval_audit`.`created_at`, NULL)) AS `first_eval_date`,\n" +
            "MAX(`eval_audit`.`user`) AS `eval_created_by`,\n" +
            "MIN(`init_eval_apt`.`start_datetime`) AS `appointment_first_eval_date`,\n" +
            "(SELECT `csu`.`username` FROM `claim_submission` `cs1` LEFT JOIN `nymbl_master`.`user` `csu` ON `cs1`.`submitted_by_id` = `csu`.`id` WHERE `cs1`.`claim_id` = `c`.`id` ORDER BY `cs1`.`claim_file_id` DESC LIMIT 1) AS `last_submission_by`,\n" +
            "DATEDIFF(FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), FROM_UNIXTIME(MIN(`c_audit_rev`.`rev_timestamp`) / 1000, '%Y-%m-%d'))  AS `days_in_current_claim_status`,\n" +
            "DATEDIFF(FROM_UNIXTIME(UNIX_TIMESTAMP(), '%Y-%m-%d'), FROM_UNIXTIME(MIN(`rx_audit_rev`.`rev_timestamp`) / 1000, '%Y-%m-%d')) AS `days_in_current_rx_status`,\n" +
            "MIN(`ap`.`applied_date`) AS `first_payment_date`\n" +
            "FROM `prescription_l_code` `plc`\n" +
            "JOIN `prescription` `rx` ON `plc`.`prescription_id` = `rx`.`id`\n" +
            "JOIN `patient` `pt` ON `rx`.`patient_id` = `pt`.`id`\n" +
            "LEFT JOIN `prescription_audit` `rx_audit` ON `rx`.`id` = `rx_audit`.`id` AND `rx`.`nymbl_status_id` = `rx_audit`.`nymbl_status_id`\n" +
            "LEFT JOIN `audit_revision` `rx_audit_rev` ON `rx_audit`.`revision_id` = `rx_audit_rev`.`revision_id`\n" +
            "LEFT JOIN `insurance_verification` `iv` ON `rx`.`id` = `iv`.`prescription_id` AND `iv`.`carrier_type` = 'primary'\n" +
            "LEFT JOIN `insurance_verification_l_code` `ivlc` ON `plc`.`id` = `ivlc`.`prescription_l_code_id` AND `iv`.`id` = `ivlc`.`insurance_verification_id`\n" +
            "LEFT JOIN `patient_insurance` `pi` ON `iv`.`patient_insurance_id` = `pi`.`id`\n" +
            "LEFT JOIN `insurance_company` `ic` ON `pi`.`insurance_company_id` = `ic`.`id`\n" +
            "LEFT JOIN (SELECT `prescription_l_code_id`, SUM(`total_cost`) `total_cost` FROM `purchase_order_item` WHERE `prescription_l_code_id` is not null GROUP BY `prescription_l_code_id`) AS `poi_sum` ON `plc`.`id` = `poi_sum`.`prescription_l_code_id`\n" +
            "LEFT JOIN `claim` `c` ON `rx`.`id` = `c`.`prescription_id` AND `iv`.`patient_insurance_id` = `c`.`patient_insurance_id`\n" +
            "LEFT JOIN `claim_audit` `c_audit` ON `c`.`id` = `c_audit`.`id` AND `c`.`nymbl_status_id` = `c_audit`.`nymbl_status_id`\n" +
            "LEFT JOIN `audit_revision` `c_audit_rev` ON `c_audit`.`revision_id` = `c_audit_rev`.`revision_id`\n" +
            "LEFT JOIN `applied_payment` `ap` ON `ap`.`claim_id` = `c`.`id`\n" +
            "LEFT JOIN (SELECT `prescription_l_code_id`, SUM(`amount`) AS `total_applied_payment` FROM `applied_payment_l_code` GROUP BY `prescription_l_code_id`) AS `aplc_sum` ON `plc`.`id` = `aplc_sum`.`prescription_l_code_id`\n" +
            "LEFT JOIN `claim_submission` `cs` ON `cs`.`claim_id` = `c`.`id`\n" +
            "LEFT JOIN `l_code` `lc` ON `lc`.`id` = `plc`.`l_code_id`\n" +
            "LEFT JOIN `device_type` `dt` ON `dt`.`id` = `rx`.`device_type_id`\n" +
            "LEFT JOIN `branch` `pb` ON `pt`.`primary_branch_id` = `pb`.`id`\n" +
            "LEFT JOIN `branch` `rxb` ON `rx`.`branch_id` = `rxb`.`id`\n" +
            "LEFT JOIN `branch` `cb` ON `c`.`billing_branch_id` = `cb`.`id`\n" +
            "LEFT JOIN `nymbl_master`.`user` `ivu` ON `iv`.`verified_by_id` = `ivu`.`id`\n" +
            "LEFT JOIN `nymbl_master`.`user` `patu` ON `pt`.`created_by_id` = `patu`.`id`\n" +
            "LEFT JOIN `nymbl_master`.`user` `rxu` ON `rx`.`created_by_id` = `rxu`.`id`\n" +
            "LEFT JOIN `nymbl_master`.`user` `clerical` ON `rx`.`clerical_user_id` = `clerical`.`id`\n" +
            "LEFT JOIN `nymbl_master`.`user` `sb` ON `c`.`created_by_id` = `sb`.`id`\n" +
            "LEFT JOIN `nymbl_master`.`user` `pc` ON `pt`.`primary_practitioner_id` = `pc`.`id`\n" +
            "LEFT JOIN `nymbl_master`.`user` `tp` ON `rx`.`treating_practitioner_id` = `tp`.`id`\n" +
            "LEFT JOIN `physician` `rp` ON `rx`.`referring_physician_id` = `rp`.`id`\n" +
            "LEFT JOIN `therapist` `th` ON `rx`.`therapist_id` = `th`.`id`\n" +
            "LEFT JOIN `facility` `fac` ON `rx`.`facility_id` = `fac`.`id`\n" +
            "LEFT JOIN `nymbl_status` `rxns` ON `rx`.`nymbl_status_id` = `rxns`.`id`\n" +
            "LEFT JOIN `nymbl_status` `cns` ON `c`.`nymbl_status_id` = `cns`.`id`\n" +
            "LEFT JOIN `nymbl_master`.`user` `c_user` ON `c`.`user_id` = `c_user`.`id`\n" +
            "LEFT JOIN (\n" +
            "   SELECT `efa`.`prescription_id`, MIN(`efa`.`created_at`) `created_at`, MIN(`ar`.`user`) `user`\n" +
            "   FROM `evaluation_form_audit` `efa`\n" +
            "   LEFT JOIN `audit_revision` `ar` ON `efa`.`revision_id` = `ar`.`revision_id`\n" +
            "   WHERE `efa`.`prescription_id` IS NOT NULL\n" +
            "   GROUP BY `efa`.`prescription_id`\n" +
            ") as `eval_audit` ON `rx`.`id` = `eval_audit`.`prescription_id`" +
            "LEFT JOIN (\n" +
            "   SELECT `apt`.`prescription_id`, \n" +
            "   MAX(`apt`.`start_datetime`) AS `start_datetime`\n" +
            "   FROM `appointment` `apt`\n" +
            "   LEFT JOIN `appointment_type` `apt_type` ON `apt_type`.`id` = `apt`.`appointment_type_id`\n" +
            "   LEFT JOIN `appointment_type_status`  `apt_type_status` ON `apt_type_status`.`id` = `apt_type`.`appointment_type_status_id`\n" +
            "   WHERE `apt`.`prescription_id` IS NOT NULL AND `apt_type_status`.`key` = 'delivery'\n" +
            "   GROUP BY  `apt`.`prescription_id`\n" +
            ") AS `delivery_apt` ON `rx`.`id` = `delivery_apt`.`prescription_id`\n" +
            "LEFT JOIN (\n" +
            "   SELECT `apt`.`prescription_id`, \n" +
            "   MAX(`apt`.`start_datetime`) AS `start_datetime`\n" +
            "   FROM `appointment` `apt`\n" +
            "   LEFT JOIN `appointment_type` `apt_type` ON `apt_type`.`id` = `apt`.`appointment_type_id`\n" +
            "   LEFT JOIN `appointment_type_status`  `apt_type_status` ON `apt_type_status`.`id` = `apt_type`.`appointment_type_status_id`\n" +
            "   WHERE `apt`.`prescription_id` IS NOT NULL AND `apt_type_status`.`key` = 'initial_eval'\n" +
            "   GROUP BY  `apt`.`prescription_id`\n" +
            ") as `init_eval_apt` ON `rx`.`id` = `init_eval_apt`.`prescription_id`\n" +
            "WHERE `rx`.`active` = TRUE AND `pt`.`active` = TRUE\n" +
            "GROUP BY `rx`.`patient_id`, `c`.`id`, `plc`.`id`, `ivlc`.`allowable_fee`, `ivlc`.`billing_fee`, `ivlc`.`covered`, `ivlc`.`bill`, `ivlc`.`total_allowable`, `ivlc`.`total_charge`, `iv`.`verified_by_id`, `iv`.`verified_on`, `ic`.`id`, `poi_sum`.`total_cost`, `aplc_sum`.`total_applied_payment`";
}
